import axios, { AxiosResponse } from 'axios';
import * as vscode from 'vscode';
import { ILLMService, LLMMessage, LLMResponse, LLMProvider, LLMStreamChunk } from '../interfaces/ILLMService';
import { AITool } from '../interfaces/IAIToolsService';
import { StreamingController } from './StreamingController';
import { ICacheService } from '../interfaces/ICacheService';

// Declare fetch as global for TypeScript
declare const fetch: any;

export class LLMService implements ILLMService {
    private conversationHistory: Map<string, LLMMessage[]> = new Map();
    private conversationCosts: Map<string, number> = new Map();
    private conversationTokens: Map<string, { input: number; output: number }> = new Map();
    private readonly maxHistoryLength = 50;
    private cacheService?: ICacheService;
    private streamingController: StreamingController;

    constructor(cacheService?: ICacheService) {
        this.cacheService = cacheService;
        this.streamingController = new StreamingController({
            maxRetries: 3,
            retryDelay: 1000,
            reconnectTimeout: 30000,
            pauseBufferSize: 100
        });
    }

    public async sendMessage(
        messages: LLMMessage[],
        provider: string,
        model: string,
        apiKey: string,
        baseUrl?: string,
        conversationId?: string
    ): Promise<LLMResponse> {
        try {
            // Store conversation history if conversationId provided
            if (conversationId) {
                this.updateConversationHistory(conversationId, messages);
            }

            // Check cache for similar requests (excluding system messages for caching)
            const cacheKey = this.generateCacheKey(messages, provider, model);
            if (this.cacheService) {
                const cached = this.cacheService.get<LLMResponse>(cacheKey);
                if (cached) {
                    // Add cached response to history
                    if (conversationId && cached.content) {
                        this.addToConversationHistory(conversationId, {
                            role: 'assistant',
                            content: cached.content
                        });
                    }
                    return cached;
                }
            }

            const response = await this.makeAPIRequest(messages, provider, model, apiKey, baseUrl);

            // Cache the response (with 15 minute TTL for API responses)
            if (this.cacheService) {
                this.cacheService.set(cacheKey, response, 15 * 60 * 1000);
            }

            // Add assistant response to history and track costs/tokens
            if (conversationId && response.content) {
                this.addToConversationHistory(conversationId, {
                    role: 'assistant',
                    content: response.content
                });

                // Update conversation costs and tokens
                this.updateConversationCost(conversationId, response.cost);
                this.updateConversationTokens(conversationId, response.tokensUsed);
            }

            return response;
        } catch (error) {
            console.error('LLM Service Error:', error);
            throw new Error(`Failed to get response from ${provider}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    public async sendMessageStream(
        messages: LLMMessage[],
        provider: string,
        model: string,
        apiKey: string,
        onChunk: (chunk: LLMStreamChunk) => void,
        baseUrl?: string,
        conversationId?: string,
        abortController?: AbortController,
        availableTools?: AITool[]
    ): Promise<LLMResponse> {
        try {
            // Store conversation history if conversationId provided
            if (conversationId) {
                this.updateConversationHistory(conversationId, messages);
            }

            // Generate stream ID
            const streamId = `stream_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

            // Create abort controller if not provided
            const controller = abortController || new AbortController();

            // Register stream with controller
            this.streamingController.registerStream(streamId, controller);

            // Enhanced chunk handler with streaming controls
            const enhancedOnChunk = async (chunk: LLMStreamChunk) => {
                // Check if stream is cancelled
                if (!this.streamingController.isStreamActive(streamId)) {
                    return;
                }

                // Handle pause state
                await this.streamingController.checkPauseState(streamId);

                // Update last chunk time
                this.streamingController.updateLastChunkTime(streamId);

                // Buffer chunk if paused, otherwise send immediately
                if (!this.streamingController.bufferChunk(streamId, chunk.content)) {
                    onChunk(chunk);
                }
            };

            // Make streaming API request with retry logic
            const response = await this.makeStreamingAPIRequestWithRetry(
                messages,
                provider,
                model,
                apiKey,
                enhancedOnChunk,
                streamId,
                controller,
                baseUrl
            );

            // Cache the final response
            if (this.cacheService && response.content) {
                const cacheKey = this.generateCacheKey(messages, provider, model);
                this.cacheService.set(cacheKey, response, 15 * 60 * 1000); // 15 minutes
            }

            // Add final response to conversation history
            if (conversationId && response.content) {
                this.addToConversationHistory(conversationId, {
                    role: 'assistant',
                    content: response.content
                });

                // Update conversation cost and tokens
                const currentCost = this.conversationCosts.get(conversationId) || 0;
                this.conversationCosts.set(conversationId, currentCost + response.cost);

                const currentTokens = this.conversationTokens.get(conversationId) || { input: 0, output: 0 };
                this.conversationTokens.set(conversationId, {
                    input: currentTokens.input + response.tokensUsed.input,
                    output: currentTokens.output + response.tokensUsed.output
                });
            }

            // Mark stream as completed
            this.streamingController.completeStream(streamId);

            return response;

        } catch (error) {
            console.error('LLM streaming request failed:', error);

            // Handle error through streaming controller
            const streamId = `stream_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
            await this.streamingController.handleStreamError(
                streamId,
                error as Error,
                async () => {
                    // Retry logic would go here
                    throw error;
                }
            );

            throw new Error(`LLM streaming request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    public cancelStream(streamId: string): void {
        this.streamingController.cancelStream(streamId);
    }

    private async makeStreamingAPIRequestWithRetry(
        messages: LLMMessage[],
        provider: string,
        model: string,
        apiKey: string,
        onChunk: (chunk: LLMStreamChunk) => void,
        streamId: string,
        abortController: AbortController,
        baseUrl?: string
    ): Promise<LLMResponse> {
        const makeRequest = async (): Promise<LLMResponse> => {
            return this.makeStreamingAPIRequest(
                messages,
                provider,
                model,
                apiKey,
                onChunk,
                streamId,
                abortController,
                baseUrl
            );
        };

        try {
            return await makeRequest();
        } catch (error) {
            // Attempt reconnection through streaming controller
            const reconnected = await this.streamingController.handleStreamError(
                streamId,
                error as Error,
                async () => {
                    await makeRequest();
                }
            );

            if (!reconnected) {
                throw error;
            }

            // If reconnected successfully, the makeRequest call in handleStreamError
            // should have returned the result
            return await makeRequest();
        }
    }

    private async makeAPIRequest(
        messages: LLMMessage[],
        provider: string,
        model: string,
        apiKey: string,
        baseUrl?: string
    ): Promise<LLMResponse> {
            switch (provider.toLowerCase()) {
                case 'deepseek': {
                    return this.callDeepSeek(messages, model, apiKey);
                }
                case 'groq': {
                    return this.callGroq(messages, model, apiKey);
                }
                case 'openrouter': {
                    return this.callOpenRouter(messages, model, apiKey);
                }
                case 'local': {
                    return this.callLocalLLM(messages, model, baseUrl);
                }
                default: {
                    throw new Error(`Unsupported provider: ${provider}. Supported providers: deepseek, groq, openrouter, local`);
                }
            }
    }

    private async makeStreamingAPIRequest(
        messages: LLMMessage[],
        provider: string,
        model: string,
        apiKey: string,
        onChunk: (chunk: LLMStreamChunk) => void,
        streamId: string,
        abortController: AbortController,
        baseUrl?: string
    ): Promise<LLMResponse> {
        try {
            switch (provider.toLowerCase()) {
                case 'deepseek': {
                    return await this.callDeepSeekStream(messages, model, apiKey, onChunk, streamId, abortController);
                }
                case 'groq': {
                    return await this.callGroqStream(messages, model, apiKey, onChunk, streamId, abortController);
                }
                case 'openrouter': {
                    return await this.callOpenRouterStream(messages, model, apiKey, onChunk, streamId, abortController);
                }
                case 'local': {
                    return await this.callLocalLLMStream(messages, model, onChunk, streamId, abortController, baseUrl);
                }
                default: {
                    // Fallback to non-streaming for unsupported providers
                    const response = await this.makeAPIRequest(messages, provider, model, apiKey, baseUrl);
                    onChunk({
                        content: response.content,
                        isComplete: true,
                        tokensUsed: response.tokensUsed,
                        cost: response.cost,
                        model: response.model,
                        streamId
                    });
                    return response;
                }
            }
        } catch (error) {
            // Normalize errors across all providers
            const normalizedError = this.normalizeStreamingError(error, provider);

            // Send error chunk to UI
            onChunk({
                content: '',
                isComplete: true,
                streamId,
                error: normalizedError
            });

            throw normalizedError;
        }
    }

    private async checkNetworkConnectivity(): Promise<{connected: boolean, latency?: number}> {
        try {
            const start = Date.now();
            await axios.get('https://www.google.com', { timeout: 3000 });
            return {connected: true, latency: Date.now() - start};
        } catch {
            return {connected: false};
        }
    }

    private async checkApiEndpointReachable(url: string): Promise<{reachable: boolean, latency?: number, dnsResolution?: number}> {
        try {
            // Test DNS resolution
            const dnsStart = Date.now();
            await axios.get(url, { timeout: 1000 });
            const dnsResolution = Date.now() - dnsStart;

            // Test API endpoint
            const apiStart = Date.now();
            const response = await axios.head(url, { timeout: 3000 });
            return {
                reachable: response.status < 500,
                latency: Date.now() - apiStart,
                dnsResolution
            };
        } catch {
            return {reachable: false};
        }
    }

    private isNetworkError(error: any): boolean {
        const networkErrorCodes = [
            'ECONNRESET', 'ENOTFOUND', 'ETIMEDOUT', 
            'ECONNREFUSED', 'EHOSTUNREACH', 'EPIPE'
        ];
        return networkErrorCodes.includes(error.code);
    }

    private getNetworkDiagnostics(): string {
        const conn = (navigator as any).connection;
        return `
Network Diagnostics:
- Online: ${navigator.onLine ? 'Yes' : 'No'}
- Connection Type: ${conn?.type || 'Unknown'}
- Effective Type: ${conn?.effectiveType || 'Unknown'}
- Downlink: ${conn?.downlink || 'Unknown'} Mbps
- RTT: ${conn?.rtt || 'Unknown'} ms
- Save Data: ${conn?.saveData ? 'Enabled' : 'Disabled'}
        `.trim();
    }

    private async measureProviderLatency(provider: string): Promise<number> {
        const testUrls = {
            deepseek: 'https://api.deepseek.com/v1',
            groq: 'https://api.groq.com/openai/v1',
            openrouter: 'https://openrouter.ai/api/v1'
        };

        const url = testUrls[provider as keyof typeof testUrls];
        if (!url) return -1;

        try {
            const start = Date.now();
            await axios.head(url, { timeout: 3000 });
            return Date.now() - start;
        } catch {
            return -1;
        }
    }

    private normalizeStreamingError(error: any, provider: string): Error {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';

        // Check for common error patterns
        if (errorMessage.includes('401') || errorMessage.includes('unauthorized')) {
            return new Error(`Authentication failed for ${provider}. Please check your API key.`);
        }

        if (errorMessage.includes('429') || errorMessage.includes('rate limit')) {
            return new Error(`Rate limit exceeded for ${provider}. Please try again later.`);
        }

        if (errorMessage.includes('403') || errorMessage.includes('forbidden')) {
            return new Error(`Access denied for ${provider}. Check your permissions and billing status.`);
        }

        if (errorMessage.includes('404') || errorMessage.includes('not found')) {
            return new Error(`Model or endpoint not found for ${provider}. Please check your configuration.`);
        }

        if (errorMessage.includes('timeout') || errorMessage.includes('ECONNRESET')) {
            return new Error(`Network timeout for ${provider}. Please check your connection and try again.`);
        }

        if (errorMessage.includes('quota') || errorMessage.includes('billing')) {
            return new Error(`Quota exceeded or billing issue for ${provider}. Please check your account.`);
        }

        // Return original error with provider context
        return new Error(`${provider} streaming error: ${errorMessage}`);
    }







    private async callOpenRouter(
        messages: LLMMessage[],
        model: string,
        apiKey: string
    ): Promise<LLMResponse> {
        const url = 'https://openrouter.ai/api/v1/chat/completions';
        
        const response: AxiosResponse = await axios.post(url, {
            model,
            messages,
            temperature: 0.7,
            max_tokens: 4000
        }, {
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://github.com/jerryjoo/v1b3-sama',
                'X-Title': 'V1b3-Sama'
            }
        });

        const data = response.data;
        const content = data.choices[0]?.message?.content || '';
        const usage = data.usage || {};

        return {
            content,
            tokensUsed: {
                input: usage.prompt_tokens || 0,
                output: usage.completion_tokens || 0
            },
            cost: 0, // OpenRouter handles billing separately
            model
        };
    }



    private async callLocalLLM(
        messages: LLMMessage[],
        model: string,
        baseUrl?: string
    ): Promise<LLMResponse> {
        const url = `${baseUrl || 'http://localhost:11434'}/api/chat`;

        const response: AxiosResponse = await axios.post(url, {
            model,
            messages,
            stream: false
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = response.data;
        const content = data.message?.content || '';

        return {
            content,
            tokensUsed: {
                input: 0, // Local models typically don't report token usage
                output: 0
            },
            cost: 0, // Local models are free
            model
        };
    }

    private async callDeepSeek(
        messages: LLMMessage[],
        model: string,
        apiKey: string,
        maxRetries = 5,  // Increased from 3
        initialDelay = 1000,
        maxDelay = 30000  // New max delay cap
    ): Promise<LLMResponse> {
        // Validate API key format
        if (!apiKey || typeof apiKey !== 'string' || apiKey.trim().length === 0) {
            throw new Error('DeepSeek API key is required and must be a valid string');
        }

        const cleanApiKey = apiKey.trim();
        const url = 'https://api.deepseek.com/v1/chat/completions';

        let lastError: Error | null = null;
        let delay = initialDelay;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                console.log(`Attempting DeepSeek API call (Attempt ${attempt + 1}/${maxRetries + 1})...`);
                
                const response: AxiosResponse = await axios.post(url, {
                    model,
                    messages,
                    temperature: 0.7,
                    max_tokens: 4000,
                    stream: false
                }, {
                    headers: {
                        'Authorization': `Bearer ${cleanApiKey}`,
                        'Content-Type': 'application/json',
                        'User-Agent': 'V1b3-Sama-Extension/5.0.2'
                    },
                    timeout: 30000
                });

                const data = response.data;
                const content = data.choices[0]?.message?.content || '';
                const usage = data.usage || {};

                // Log rate limit headers if available
                if (response.headers['x-ratelimit-limit']) {
                    console.log(`DeepSeek Rate Limits - Limit: ${response.headers['x-ratelimit-limit']}, Remaining: ${response.headers['x-ratelimit-remaining']}, Reset: ${response.headers['x-ratelimit-reset']}`);
                }

                return {
                    content,
                    tokensUsed: {
                        input: usage.prompt_tokens || 0,
                        output: usage.completion_tokens || 0
                    },
                    cost: this.calculateDeepSeekCost(usage.prompt_tokens || 0, usage.completion_tokens || 0, model),
                    model
                };
            } catch (error: any) {
                lastError = error;
                
                if (error.response) {
                    const status = error.response.status;
                    const errorData = error.response.data;

                    // Log detailed error information
                    console.error(`DeepSeek API Error (Attempt ${attempt + 1}):`, {
                        status,
                        error: errorData?.error || error.message,
                        headers: error.response.headers
                    });

                    // Non-retryable errors
                    if ([400, 401, 403, 404].includes(status)) {
                        throw new Error(`DeepSeek API error (${status}): ${errorData?.error?.message || error.message}`);
                    }

                    // Rate limit handling
                    if (status === 429) {
                        const retryAfter = error.response.headers['retry-after'] || delay;
                        delay = parseInt(retryAfter, 10) * 1000;
                        console.log(`Rate limited. Retrying in ${delay}ms...`);
                    }
                } else if (this.isNetworkError(error)) {
                    console.error('Network error:', error.message);
                    // For network errors, use a more aggressive retry strategy
                    if (error.code === 'ETIMEDOUT') {
                        delay = Math.min(5000, delay * 3); // Cap timeout retries at 5s
                    }
                    // Continue with retry
                } else {
                    console.error('Unexpected error:', error);
                    throw new Error(`DeepSeek request failed: ${error.message}`);
                }

                if (attempt < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, delay));
                    delay *= 2; // Exponential backoff
                }
            }
        }

        throw lastError || new Error('Max retries reached for DeepSeek API');
    }

    private async callGroq(
        messages: LLMMessage[],
        model: string,
        apiKey: string
    ): Promise<LLMResponse> {
        // Validate API key format
        if (!apiKey || typeof apiKey !== 'string' || apiKey.trim().length === 0) {
            throw new Error('Groq API key is required and must be a valid string');
        }

        const cleanApiKey = apiKey.trim();
        const url = 'https://api.groq.com/openai/v1/chat/completions';

        try {
            const response: AxiosResponse = await axios.post(url, {
                model,
                messages,
                temperature: 0.7,
                max_tokens: 4000,
                stream: false
            }, {
                headers: {
                    'Authorization': `Bearer ${cleanApiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });

            const data = response.data;
            const content = data.choices[0]?.message?.content || '';
            const usage = data.usage || {};

            return {
                content,
                tokensUsed: {
                    input: usage.prompt_tokens || 0,
                    output: usage.completion_tokens || 0
                },
                cost: this.calculateGroqCost(usage.prompt_tokens || 0, usage.completion_tokens || 0, model),
                model
            };
        } catch (error: any) {
            if (error.response) {
                const status = error.response.status;
                const errorData = error.response.data;

                switch (status) {
                    case 401:
                        throw new Error('Groq authentication failed. Please check your API key.');
                    case 403:
                        throw new Error('Groq access denied. Check your API key permissions and billing status.');
                    case 429:
                        throw new Error('Groq rate limit exceeded. Please try again later.');
                    case 500:
                        throw new Error('Groq server error. Please try again later.');
                    default:
                        throw new Error(`Groq API error (${status}): ${errorData?.error?.message || error.message}`);
                }
            } else if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND') {
                throw new Error('Groq connection failed. Please check your internet connection.');
            } else {
                throw new Error(`Groq request failed: ${error.message}`);
            }
        }
    }

    private async callDeepSeekStream(
        messages: LLMMessage[],
        model: string,
        apiKey: string,
        onChunk: (chunk: LLMStreamChunk) => void,
        streamId: string,
        abortController: AbortController,
        maxRetries = 3,
        initialDelay = 1000,
        attempt = 0
    ): Promise<LLMResponse> {
        // Validate API key format
        if (!apiKey || typeof apiKey !== 'string' || apiKey.trim().length === 0) {
            throw new Error('DeepSeek API key is required and must be a valid string');
        }

        const cleanApiKey = apiKey.trim();
        const url = 'https://api.deepseek.com/v1/chat/completions';

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${cleanApiKey}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'V1b3-Sama-Extension/5.0.2'
                },
                body: JSON.stringify({
                    model,
                    messages,
                    temperature: 0.7,
                    max_tokens: 4000,
                    stream: true
                }),
                signal: abortController.signal
            });

            if (!response.ok) {
                const errorText = await response.text();
                let errorMessage = `DeepSeek API error: ${response.status} ${response.statusText}`;

                try {
                    const errorData = JSON.parse(errorText);
                    if (errorData.error?.message) {
                        errorMessage += ` - ${errorData.error.message}`;
                    }
                } catch {
                    // If we can't parse the error, just use the status text
                }

                switch (response.status) {
                    case 401:
                        throw new Error('DeepSeek authentication failed. Please check your API key.');
                    case 403:
                        throw new Error('DeepSeek access denied. Check your API key permissions and billing status.');
                    case 429:
                        throw new Error('DeepSeek rate limit exceeded. Please try again later.');
                    case 500:
                        throw new Error('DeepSeek server error. Please try again later.');
                    default:
                        throw new Error(errorMessage);
                }
            }

            const reader = response.body?.getReader();
            if (!reader) {
                throw new Error('Failed to get DeepSeek response stream reader');
            }

            let fullContent = '';
            let totalTokens = { input: 0, output: 0 };
            const decoder = new TextDecoder();

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n').filter(line => line.trim() !== '');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);

                            if (data === '[DONE]') {
                                onChunk({
                                    content: '',
                                    isComplete: true,
                                    tokensUsed: totalTokens,
                                    cost: this.calculateDeepSeekCost(totalTokens.input, totalTokens.output, model),
                                    model,
                                    streamId
                                });
                                break;
                            }

                            try {
                                const parsed = JSON.parse(data);
                                const delta = parsed.choices?.[0]?.delta;

                                if (delta?.content) {
                                    fullContent += delta.content;
                                    onChunk({
                                        content: delta.content,
                                        isComplete: false,
                                        streamId
                                    });
                                }

                                // Update token usage if available
                                if (parsed.usage) {
                                    totalTokens = {
                                        input: parsed.usage.prompt_tokens || 0,
                                        output: parsed.usage.completion_tokens || 0
                                    };
                                }
                            } catch (parseError) {
                                console.warn('Failed to parse DeepSeek streaming chunk:', parseError);
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }

            return {
                content: fullContent,
                tokensUsed: totalTokens,
                cost: this.calculateDeepSeekCost(totalTokens.input, totalTokens.output, model),
                model,
                isStreaming: true,
                streamId
            };
        } catch (error: any) {
            // Enhanced error handling for DeepSeek streaming
            if (error.name === 'AbortError') {
                throw new Error('DeepSeek request was cancelled');
            } else if (this.isNetworkError(error)) {
                // Run comprehensive diagnostics
                const {connected: networkAvailable, latency: netLatency} = await this.checkNetworkConnectivity();
                const {reachable: apiReachable, latency: apiLatency, dnsResolution} = 
                    await this.checkApiEndpointReachable('https://api.deepseek.com');
                const diagnostics = this.getNetworkDiagnostics();
                const providerLatency = await this.measureProviderLatency('deepseek');

                if (attempt < maxRetries) {
                    console.log(`Streaming connection failed (attempt ${attempt + 1})`, {
                        error: error.message,
                        networkAvailable,
                        netLatency,
                        apiReachable, 
                        apiLatency,
                        dnsResolution,
                        providerLatency,
                        diagnostics
                    });
                    
                    await new Promise(resolve => setTimeout(resolve, initialDelay));
                    return this.callDeepSeekStream(
                        messages, model, apiKey, onChunk, streamId, 
                        abortController, maxRetries, initialDelay * 2,
                        attempt + 1
                    );
                }
                
                throw new Error(
                    `DeepSeek connection failed after ${maxRetries} attempts.\n` +
                    `Network Status: ${networkAvailable ? 'Connected' : 'Disconnected'}\n` +
                    `Network Latency: ${netLatency || 'N/A'}ms\n` +
                    `API Status: ${apiReachable ? 'Reachable' : 'Unreachable'}\n` +
                    `API Latency: ${apiLatency || 'N/A'}ms\n` +
                    `DNS Resolution: ${dnsResolution || 'N/A'}ms\n` +
                    `Provider Latency: ${providerLatency !== -1 ? providerLatency + 'ms' : 'Unavailable'}\n` +
                    `${diagnostics}\n` +
                    `Please check your internet connection and try again.`
                );
            } else {
                throw error; // Re-throw the error as-is if it's already formatted
            }
        }
    }

    private async callGroqStream(
        messages: LLMMessage[],
        model: string,
        apiKey: string,
        onChunk: (chunk: LLMStreamChunk) => void,
        streamId: string,
        abortController: AbortController
    ): Promise<LLMResponse> {
        const cleanApiKey = apiKey.trim();
        const url = 'https://api.groq.com/openai/v1/chat/completions';

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${cleanApiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model,
                    messages,
                    temperature: 0.7,
                    max_tokens: 4000,
                    stream: true
                }),
                signal: abortController.signal
            });

            if (!response.ok) {
                const errorText = await response.text();
                let errorMessage = `Groq API error: ${response.status} ${response.statusText}`;

                try {
                    const errorData = JSON.parse(errorText);
                    if (errorData.error?.message) {
                        errorMessage += ` - ${errorData.error.message}`;
                    }
                } catch {
                    // If we can't parse the error, just use the status text
                }

                switch (response.status) {
                    case 401:
                        throw new Error('Groq authentication failed. Please check your API key.');
                    case 403:
                        throw new Error('Groq access denied. Check your API key permissions and billing status.');
                    case 429:
                        throw new Error('Groq rate limit exceeded. Please try again later.');
                    case 500:
                        throw new Error('Groq server error. Please try again later.');
                    default:
                        throw new Error(errorMessage);
                }
            }

            const reader = response.body?.getReader();
            if (!reader) {
                throw new Error('Failed to get Groq response stream reader');
            }

            let fullContent = '';
            let totalTokens = { input: 0, output: 0 };
            const decoder = new TextDecoder();

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n').filter(line => line.trim() !== '');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);

                            if (data === '[DONE]') {
                                onChunk({
                                    content: '',
                                    isComplete: true,
                                    tokensUsed: totalTokens,
                                    cost: this.calculateGroqCost(totalTokens.input, totalTokens.output, model),
                                    model,
                                    streamId
                                });
                                break;
                            }

                            try {
                                const parsed = JSON.parse(data);
                                const delta = parsed.choices?.[0]?.delta;

                                if (delta?.content) {
                                    fullContent += delta.content;
                                    onChunk({
                                        content: delta.content,
                                        isComplete: false,
                                        streamId
                                    });
                                }

                                // Update token usage if available
                                if (parsed.usage) {
                                    totalTokens = {
                                        input: parsed.usage.prompt_tokens || 0,
                                        output: parsed.usage.completion_tokens || 0
                                    };
                                }
                            } catch (parseError) {
                                console.warn('Failed to parse Groq streaming chunk:', parseError);
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }

            return {
                content: fullContent,
                tokensUsed: totalTokens,
                cost: this.calculateGroqCost(totalTokens.input, totalTokens.output, model),
                model,
                isStreaming: true,
                streamId
            };
        } catch (error: any) {
            // Enhanced error handling for Groq streaming
            if (error.name === 'AbortError') {
                throw new Error('Groq request was cancelled');
            } else if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND') {
                throw new Error('Groq connection failed. Please check your internet connection.');
            } else {
                throw error; // Re-throw the error as-is if it's already formatted
            }
        }
    }

    private async callOpenRouterStream(
        messages: LLMMessage[],
        model: string,
        apiKey: string,
        onChunk: (chunk: LLMStreamChunk) => void,
        streamId: string,
        abortController: AbortController
    ): Promise<LLMResponse> {
        const url = 'https://openrouter.ai/api/v1/chat/completions';

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://github.com/your-repo/v1b3-sama',
                'X-Title': 'V1b3-Sama Extension'
            },
            body: JSON.stringify({
                model,
                messages,
                temperature: 0.7,
                max_tokens: 4000,
                stream: true
            }),
            signal: abortController.signal
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const reader = response.body?.getReader();
        if (!reader) {
            throw new Error('Failed to get response stream reader');
        }

        let fullContent = '';
        let totalTokens = { input: 0, output: 0 };
        const decoder = new TextDecoder();

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n').filter(line => line.trim() !== '');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);

                        if (data === '[DONE]') {
                            onChunk({
                                content: '',
                                isComplete: true,
                                tokensUsed: totalTokens,
                                cost: this.calculateOpenRouterCost(totalTokens.input, totalTokens.output, model),
                                model,
                                streamId
                            });
                            break;
                        }

                        try {
                            const parsed = JSON.parse(data);
                            const delta = parsed.choices?.[0]?.delta;

                            if (delta?.content) {
                                fullContent += delta.content;
                                onChunk({
                                    content: delta.content,
                                    isComplete: false,
                                    streamId
                                });
                            }

                            // Update token usage if available
                            if (parsed.usage) {
                                totalTokens = {
                                    input: parsed.usage.prompt_tokens || 0,
                                    output: parsed.usage.completion_tokens || 0
                                };
                            }
                        } catch (parseError) {
                            console.warn('Failed to parse OpenRouter streaming chunk:', parseError);
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }

        return {
            content: fullContent,
            tokensUsed: totalTokens,
            cost: this.calculateOpenRouterCost(totalTokens.input, totalTokens.output, model),
            model,
            isStreaming: true,
            streamId
        };
    }



    private async callLocalLLMStream(
        messages: LLMMessage[],
        model: string,
        onChunk: (chunk: LLMStreamChunk) => void,
        streamId: string,
        abortController: AbortController,
        baseUrl?: string
    ): Promise<LLMResponse> {
        // For now, fallback to non-streaming
        const response = await this.callLocalLLM(messages, model, baseUrl);
        onChunk({
            content: response.content,
            isComplete: true,
            tokensUsed: response.tokensUsed,
            cost: response.cost,
            model: response.model,
            streamId
        });
        return { ...response, isStreaming: true, streamId };
    }

    private calculateGroqCost(inputTokens: number, outputTokens: number, model: string): number {
        // Groq pricing (as of 2025) - per 1M tokens
        const costs: Record<string, { input: number; output: number }> = {
            'llama-3.3-70b-versatile': { input: 0.59 / 1000000, output: 0.79 / 1000000 },
            'llama-3.1-8b-instant': { input: 0.05 / 1000000, output: 0.08 / 1000000 },
            'llama3-70b-8192': { input: 0.59 / 1000000, output: 0.79 / 1000000 },
            'llama3-8b-8192': { input: 0.05 / 1000000, output: 0.08 / 1000000 },
            'mixtral-8x7b-32768': { input: 0.24 / 1000000, output: 0.24 / 1000000 },
            'gemma2-9b-it': { input: 0.20 / 1000000, output: 0.20 / 1000000 },
            'qwen3-32b': { input: 0.29 / 1000000, output: 0.59 / 1000000 },
            'qwen-qwq-32b': { input: 0.29 / 1000000, output: 0.39 / 1000000 },
            'deepseek-r1-distill-llama-70b': { input: 0.75 / 1000000, output: 0.99 / 1000000 },
            'llama-guard-3-8b': { input: 0.20 / 1000000, output: 0.20 / 1000000 }
        };

        const modelCost = costs[model] || costs['llama-3.1-8b-instant']; // Default to cheapest
        return (inputTokens * modelCost.input) + (outputTokens * modelCost.output);
    }

    private calculateOpenRouterCost(inputTokens: number, outputTokens: number, model: string): number {
        // OpenRouter handles billing separately, so we return 0
        // In a real implementation, you might want to track usage for reporting
        return 0;
    }

    private calculateDeepSeekCost(inputTokens: number, outputTokens: number, model: string): number {
        // DeepSeek pricing (as of 2025) - per 1M tokens (standard pricing, cache miss)
        const costs: Record<string, { input: number; output: number }> = {
            'deepseek-coder': { input: 0.27 / 1000000, output: 1.10 / 1000000 },
            'deepseek-chat': { input: 0.27 / 1000000, output: 1.10 / 1000000 },
            'deepseek-reasoner': { input: 0.55 / 1000000, output: 2.19 / 1000000 } // Higher for reasoning model
        };

        const modelCost = costs[model] || costs['deepseek-chat'];
        return (inputTokens * modelCost.input) + (outputTokens * modelCost.output);
    }

    private calculateCostForModel(inputTokens: number, outputTokens: number, model: string, baseUrl?: string): number {
        // Determine provider based on model name or base URL
        if (model.startsWith('deepseek-') || baseUrl?.includes('deepseek.com')) {
            return this.calculateDeepSeekCost(inputTokens, outputTokens, model);
        } else if (model.startsWith('llama-') || model.startsWith('mixtral-') || model.startsWith('gemma')) {
            return this.calculateGroqCost(inputTokens, outputTokens, model);
        } else {
            // Default to OpenRouter (free) for unknown models
            return this.calculateOpenRouterCost(inputTokens, outputTokens, model);
        }
    }

    public getConversationHistory(conversationId: string): LLMMessage[] {
        return this.conversationHistory.get(conversationId) || [];
    }

    public clearConversationHistory(conversationId: string): void {
        this.conversationHistory.delete(conversationId);
        this.conversationCosts.delete(conversationId);
        this.conversationTokens.delete(conversationId);
    }

    public addToConversationHistory(conversationId: string, message: LLMMessage): void {
        const history = this.conversationHistory.get(conversationId) || [];
        history.push(message);

        // Trim history if too long
        if (history.length > this.maxHistoryLength) {
            history.splice(0, history.length - this.maxHistoryLength);
        }

        this.conversationHistory.set(conversationId, history);
    }

    public updateConversationHistory(conversationId: string, messages: LLMMessage[]): void {
        const history = this.conversationHistory.get(conversationId) || [];

        // Add new messages to history
        messages.forEach(message => {
            if (message.role === 'user') {
                history.push(message);
            }
        });

        // Trim history if too long
        if (history.length > this.maxHistoryLength) {
            history.splice(0, history.length - this.maxHistoryLength);
        }

        this.conversationHistory.set(conversationId, history);
    }

    public getConversationCost(conversationId: string): number {
        return this.conversationCosts.get(conversationId) || 0;
    }

    public getConversationTokens(conversationId: string): { input: number; output: number } {
        return this.conversationTokens.get(conversationId) || { input: 0, output: 0 };
    }

    private updateConversationCost(conversationId: string, cost: number): void {
        const currentCost = this.conversationCosts.get(conversationId) || 0;
        this.conversationCosts.set(conversationId, currentCost + cost);
    }

    private updateConversationTokens(conversationId: string, tokens: { input: number; output: number }): void {
        const currentTokens = this.conversationTokens.get(conversationId) || { input: 0, output: 0 };
        this.conversationTokens.set(conversationId, {
            input: currentTokens.input + tokens.input,
            output: currentTokens.output + tokens.output
        });
    }

    private generateCacheKey(messages: LLMMessage[], provider: string, model: string): string {
        // Create a cache key based on the last user message, provider, and model
        // Exclude system messages to allow for better caching
        const userMessages = messages.filter(m => m.role === 'user');
        const lastUserMessage = userMessages[userMessages.length - 1];

        if (!lastUserMessage) {
            return `${provider}:${model}:empty`;
        }

        // Create a hash of the message content for consistent caching
        const messageHash = this.simpleHash(lastUserMessage.content);
        return `llm:${provider}:${model}:${messageHash}`;
    }

    private simpleHash(str: string): string {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(36);
    }
}
